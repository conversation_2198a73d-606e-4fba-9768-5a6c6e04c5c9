#!/usr/bin/env python3
"""
SAFE Key Compatibility Test
Tests different key sources to find the correct decryption method
NO SYSTEM MODIFICATIONS - READ ONLY
"""
import os
import sys
import base64
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from dotenv import load_dotenv

# Load environment safely
load_dotenv()

def test_key_source_1():
    """Test with src/utils/cryptography/fernet.key"""
    print("\n🧪 [TEST-1] Testing src/utils/cryptography/fernet.key")
    try:
        with open('src/utils/cryptography/fernet.key', 'rb') as f:
            key = f.read()
        
        fernet = Fernet(key)
        encrypted_value = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
        
        # Attempt decryption
        result = fernet.decrypt(encrypted_value.encode()).decode()
        print(f"  ✅ SUCCESS! Key works - result starts with: {result[:50]}")
        return True
        
    except Exception as e:
        print(f"  ❌ Failed: {e}")
        return False

def test_key_source_2():
    """Test with .fernet_key file (FERNET_KEY=... format)"""
    print("\n🧪 [TEST-2] Testing .fernet_key file")
    try:
        with open('.fernet_key', 'r') as f:
            content = f.read().strip()
        
        if content.startswith('FERNET_KEY='):
            key_str = content.split('FERNET_KEY=')[1]
            key = key_str.encode()
            
            fernet = Fernet(key)
            encrypted_value = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
            
            result = fernet.decrypt(encrypted_value.encode()).decode()
            print(f"  ✅ SUCCESS! Key works - result starts with: {result[:50]}")
            return True
        else:
            print("  ❌ Invalid format in .fernet_key")
            return False
            
    except Exception as e:
        print(f"  ❌ Failed: {e}")
        return False

def test_key_source_3():
    """Test with password-based key derivation (default password)"""
    print("\n🧪 [TEST-3] Testing password-based key derivation")
    try:
        password = 'default_password'
        salt = b'stable_salt_for_consistency'
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        fernet = Fernet(key)
        encrypted_value = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
        
        result = fernet.decrypt(encrypted_value.encode()).decode()
        print(f"  ✅ SUCCESS! Key works - result starts with: {result[:50]}")
        return True
        
    except Exception as e:
        print(f"  ❌ Failed: {e}")
        return False

def test_key_source_4():
    """Test with hybrid RSA-decrypted Fernet key"""
    print("\n🧪 [TEST-4] Testing hybrid RSA + Fernet system")
    try:
        # Import the hybrid system
        sys.path.append('src')
        from utils.cryptography.hybrid import HybridCrypto
        
        crypto = HybridCrypto()
        encrypted_value = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
        
        result = crypto.decrypt_value(encrypted_value)
        print(f"  ✅ SUCCESS! Hybrid key works - result starts with: {result[:50]}")
        return True
        
    except Exception as e:
        print(f"  ❌ Failed: {e}")
        return False

def test_key_source_5():
    """Test if the value is double-encrypted"""
    print("\n🧪 [TEST-5] Testing double-encryption scenarios")
    try:
        # Try with the most likely key first
        with open('src/utils/cryptography/fernet.key', 'rb') as f:
            key = f.read()
        
        fernet = Fernet(key)
        encrypted_value = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
        
        # First decryption
        first_result = fernet.decrypt(encrypted_value.encode()).decode()
        
        # Check if result is still encrypted (starts with gAAAAA)
        if first_result.startswith('gAAAAA'):
            print("  🔄 Detected double-encryption, attempting second decryption...")
            second_result = fernet.decrypt(first_result.encode()).decode()
            print(f"  ✅ SUCCESS! Double-decryption works - result starts with: {second_result[:50]}")
            return True
        else:
            print("  ℹ️ Not double-encrypted")
            return False
            
    except Exception as e:
        print(f"  ❌ Failed: {e}")
        return False

def main():
    """Run all safe tests to find working decryption method"""
    print("🛡️ [SAFE-MODE] Key Compatibility Testing - READ ONLY")
    print("=" * 60)
    
    encrypted_value = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
    if not encrypted_value:
        print("❌ No ENCRYPTED_COINBASE_PRIVATE_KEY found")
        return
    
    print(f"🔍 Testing decryption of {len(encrypted_value)}-character token...")
    
    # Test all key sources
    tests = [
        test_key_source_1,
        test_key_source_2, 
        test_key_source_3,
        test_key_source_4,
        test_key_source_5
    ]
    
    success_count = 0
    for test_func in tests:
        if test_func():
            success_count += 1
    
    print(f"\n📊 [SUMMARY] {success_count}/{len(tests)} tests successful")
    
    if success_count == 0:
        print("❌ No working decryption method found")
        print("💡 The encrypted value may be corrupted or use a different encryption method")
    else:
        print("✅ Found working decryption method(s)")
    
    print("\n✅ [SAFE-MODE] Testing complete - NO CHANGES MADE")

if __name__ == "__main__":
    main()
