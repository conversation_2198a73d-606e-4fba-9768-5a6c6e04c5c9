#!/usr/bin/env python3
"""
Fix Coinbase PEM with Correct HybridCrypto Usage
"""
import os
import sys
import base64
from dotenv import load_dotenv

# Load environment
load_dotenv()

# Add project root to path
sys.path.append('.')

def fix_and_reencrypt_correct():
    """Fix PEM format and re-encrypt correctly"""
    print("🔧 [PEM-CORRECT] Fixing and re-encrypting Coinbase private key correctly...")
    
    try:
        # Use the old encrypted key that was working for decryption
        old_encrypted_key = "gAAAAABoUHiuRQLMSJLAHHrmhZg_IM93Bc6zGzn3MJoEnHPl0FGl_j60Ya5_bZ6AYgpyqs0eeHKr4pRDrcPgVDQsyUy2E1gTvG6WE7TS-20fLJuL0ZVS2N35VERyYUPLm7G5NWKompxReZHS6kOimhCuWbUIvGlUWlVog2fMnI7fcor0lEBd26CQxRNqwR1-ihfDE5UDIpcHte3fPAoG96wVw-piOABvh_Lvh_-8PGa0a886nzDLchWFeiEDMeNPAc-Bej_XsXkABl34ApZ6NUHmt_w6hbBePIIA8zSb1RaNCzVPi36VM44pIzB4mFZa_i5_4TVQvyfiuk6DQ1LXB81XvZHe544tKSAgvdXLGasDnuqMunueQd0vbmc0v3aCz4SwjD1IfrRW"
        
        # Load HybridCrypto
        from src.utils.cryptography.hybrid import HybridCrypto
        crypto = HybridCrypto('src/utils/cryptography/private.pem')
        
        print("📥 [PEM-CORRECT] Decrypting old key...")
        
        # Decrypt the old key
        decrypted_key = crypto.decrypt_value(old_encrypted_key)
        print(f"✅ [PEM-CORRECT] Old key decrypted, length: {len(decrypted_key)}")
        
        # Analyze the decrypted content for invalid chars
        print("🔍 [PEM-CORRECT] Analyzing decrypted content...")
        invalid_chars = []
        for i, char in enumerate(decrypted_key):
            byte_val = ord(char)
            if byte_val < 32 and byte_val not in [10, 13]:  # Allow \n (10) and \r (13)
                invalid_chars.append((i, byte_val, repr(char)))
        
        if invalid_chars:
            print(f"⚠️ [PEM-CORRECT] Found {len(invalid_chars)} invalid characters:")
            for pos, val, char in invalid_chars[:5]:  # Show first 5
                print(f"   Position {pos}: byte {val} ({char})")
        
        # Fix PEM format issues
        print("🔧 [PEM-CORRECT] Fixing PEM format...")
        
        # Remove control characters except newlines
        fixed_key = ''.join(char for char in decrypted_key if ord(char) >= 32 or char in ['\n', '\r'])
        
        # Fix newline representations
        fixed_key = fixed_key.replace('\\n', '\n')
        fixed_key = fixed_key.replace('\r\n', '\n')
        fixed_key = fixed_key.replace('\r', '\n')
        
        print(f"🔧 [PEM-CORRECT] Fixed key length: {len(fixed_key)}")
        print(f"🔧 [PEM-CORRECT] First 100 chars: {repr(fixed_key[:100])}")
        
        # Test the fixed key
        print("🧪 [PEM-CORRECT] Testing fixed PEM format...")
        
        try:
            from cryptography.hazmat.primitives import serialization
            
            private_key = serialization.load_pem_private_key(
                fixed_key.encode('utf-8'),
                password=None
            )
            
            print(f"✅ [PEM-CORRECT] Fixed PEM format is valid: {type(private_key)}")
            
        except Exception as e:
            print(f"❌ [PEM-CORRECT] Fixed PEM still invalid: {e}")
            return False
        
        # Re-encrypt using the cipher directly
        print("🔐 [PEM-CORRECT] Re-encrypting with correct format...")
        
        try:
            # Encrypt the fixed key
            encrypted_data = crypto.cipher.encrypt(fixed_key.encode())
            # The result is already bytes, so we can just decode it to get the token string
            new_encrypted_key = encrypted_data.decode()
            
            print(f"✅ [PEM-CORRECT] Re-encryption successful!")
            print(f"🔐 [PEM-CORRECT] New encrypted key length: {len(new_encrypted_key)}")
            print(f"🔐 [PEM-CORRECT] New key preview: {new_encrypted_key[:50]}...")
            
            # Verify the new key can be decrypted
            print("🧪 [PEM-CORRECT] Testing new encrypted key...")
            
            test_decrypted = crypto.decrypt_value(new_encrypted_key)
            if test_decrypted == fixed_key:
                print("✅ [PEM-CORRECT] New key encryption/decryption verified!")
                
                print("\n📝 [PEM-CORRECT] Update your .env file with:")
                print(f"ENCRYPTED_COINBASE_PRIVATE_KEY={new_encrypted_key}")
                
                return new_encrypted_key
            else:
                print("❌ [PEM-CORRECT] New key verification failed!")
                print(f"   Expected length: {len(fixed_key)}")
                print(f"   Got length: {len(test_decrypted)}")
                print(f"   First 50 chars match: {fixed_key[:50] == test_decrypted[:50]}")
                return False
                
        except Exception as e:
            print(f"❌ [PEM-CORRECT] Re-encryption failed: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ [PEM-CORRECT] Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        result = fix_and_reencrypt_correct()
        if result:
            print("\n🎉 [PEM-CORRECT] Coinbase private key fix completed successfully!")
        else:
            print("\n❌ [PEM-CORRECT] Could not fix the private key")
    except KeyboardInterrupt:
        print("\n⏹️ [PEM-CORRECT] Operation cancelled by user")
    except Exception as e:
        print(f"\n💥 [PEM-CORRECT] Fatal error: {e}")
