#!/usr/bin/env python3
"""Test the fixed Coinbase credentials"""
import os
import sys
sys.path.append('.')
from dotenv import load_dotenv

load_dotenv()

try:
    from src.utils.cryptography.hybrid import HybridCrypto
    crypto = HybridCrypto('src/utils/cryptography/private.pem')
    
    # Test decryption
    encrypted = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
    decrypted = crypto.decrypt_value(encrypted)
    print(f'✅ Decryption successful: {len(decrypted)} chars')
    
    # Test PEM parsing
    from cryptography.hazmat.primitives import serialization
    key = serialization.load_pem_private_key(decrypted.encode(), password=None)
    print(f'✅ PEM parsing successful: {type(key)}')
    print('🎉 Coinbase credentials are working correctly!')
    
except Exception as e:
    print(f'❌ Error: {e}')
    import traceback
    traceback.print_exc()
