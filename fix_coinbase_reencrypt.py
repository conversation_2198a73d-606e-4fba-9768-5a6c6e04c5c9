#!/usr/bin/env python3
"""
Fix Coinbase PEM and Re-encrypt Properly
Fix the PEM format and re-encrypt with proper base64 encoding
"""
import os
import sys
import base64
from dotenv import load_dotenv

# Load environment
load_dotenv()

# Add project root to path
sys.path.append('.')

def fix_and_reencrypt():
    """Fix PEM format and re-encrypt properly"""
    print("🔧 [PEM-REENCRYPT] Fixing and re-encrypting Coinbase private key...")
    
    try:
        # Use the old encrypted key that was working for decryption
        old_encrypted_key = "gAAAAABoUHiuRQLMSJLAHHrmhZg_IM93Bc6zGzn3MJoEnHPl0FGl_j60Ya5_bZ6AYgpyqs0eeHKr4pRDrcPgVDQsyUy2E1gTvG6WE7TS-20fLJuL0ZVS2N35VERyYUPLm7G5NWKompxReZHS6kOimhCuWbUIvGlUWlVog2fMnI7fcor0lEBd26CQxRNqwR1-ihfDE5UDIpcHte3fPAoG96wVw-piOABvh_Lvh_-8PGa0a886nzDLchWFeiEDMeNPAc-Bej_XsXkABl34ApZ6NUHmt_w6hbBePIIA8zSb1RaNCzVPi36VM44pIzB4mFZa_i5_4TVQvyfiuk6DQ1LXB81XvZHe544tKSAgvdXLGasDnuqMunueQd0vbmc0v3aCz4SwjD1IfrRW"
        
        # Load HybridCrypto
        from src.utils.cryptography.hybrid import HybridCrypto
        crypto = HybridCrypto('src/utils/cryptography/private.pem')
        
        print("📥 [PEM-REENCRYPT] Decrypting old key...")
        
        # Decrypt the old key
        decrypted_key = crypto.decrypt_value(old_encrypted_key)
        print(f"✅ [PEM-REENCRYPT] Old key decrypted, length: {len(decrypted_key)}")
        
        # Fix PEM format issues
        print("🔧 [PEM-REENCRYPT] Fixing PEM format...")
        
        # Remove control characters except newlines
        fixed_key = ''.join(char for char in decrypted_key if ord(char) >= 32 or char in ['\n', '\r'])
        
        # Fix newline representations
        fixed_key = fixed_key.replace('\\n', '\n')
        fixed_key = fixed_key.replace('\r\n', '\n')
        fixed_key = fixed_key.replace('\r', '\n')
        
        # Test the fixed key
        print("🧪 [PEM-REENCRYPT] Testing fixed PEM format...")
        
        try:
            from cryptography.hazmat.primitives import serialization
            
            private_key = serialization.load_pem_private_key(
                fixed_key.encode('utf-8'),
                password=None
            )
            
            print(f"✅ [PEM-REENCRYPT] Fixed PEM format is valid: {type(private_key)}")
            
        except Exception as e:
            print(f"❌ [PEM-REENCRYPT] Fixed PEM still invalid: {e}")
            return False
        
        # Re-encrypt using the Fernet cipher directly
        print("🔐 [PEM-REENCRYPT] Re-encrypting with proper format...")
        
        try:
            # Encrypt the fixed key
            encrypted_bytes = crypto.cipher.encrypt(fixed_key.encode())
            new_encrypted_key = base64.urlsafe_b64encode(encrypted_bytes).decode()
            
            print(f"✅ [PEM-REENCRYPT] Re-encryption successful!")
            print(f"🔐 [PEM-REENCRYPT] New encrypted key length: {len(new_encrypted_key)}")
            print(f"🔐 [PEM-REENCRYPT] New key preview: {new_encrypted_key[:50]}...")
            
            # Verify the new key can be decrypted
            print("🧪 [PEM-REENCRYPT] Testing new encrypted key...")
            
            test_decrypted = crypto.decrypt_value(new_encrypted_key)
            if test_decrypted == fixed_key:
                print("✅ [PEM-REENCRYPT] New key encryption/decryption verified!")
                
                print("\n📝 [PEM-REENCRYPT] Update your .env file with:")
                print(f"ENCRYPTED_COINBASE_PRIVATE_KEY={new_encrypted_key}")
                
                return new_encrypted_key
            else:
                print("❌ [PEM-REENCRYPT] New key verification failed!")
                return False
                
        except Exception as e:
            print(f"❌ [PEM-REENCRYPT] Re-encryption failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ [PEM-REENCRYPT] Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        result = fix_and_reencrypt()
        if result:
            print("\n🎉 [PEM-REENCRYPT] Coinbase private key fix completed successfully!")
        else:
            print("\n❌ [PEM-REENCRYPT] Could not fix the private key")
    except KeyboardInterrupt:
        print("\n⏹️ [PEM-REENCRYPT] Operation cancelled by user")
    except Exception as e:
        print(f"\n💥 [PEM-REENCRYPT] Fatal error: {e}")
